from mcp.server.fastmcp import FastMCP
from DrissionPage import ChromiumPage, ChromiumOptions
import os
import requests
from urllib.parse import urljoin, urlparse
import time
import json
import re

# 创建 MCP 服务器
mcp = FastMCP("DrissionPage Server")

# 配置浏览器选项以绕过检测
def create_stealth_browser():
    """创建隐蔽模式的浏览器"""
    co = ChromiumOptions()
    # 基本隐蔽选项
    co.set_argument('--no-sandbox')
    co.set_argument('--disable-blink-features=AutomationControlled')
    co.set_argument('--disable-extensions')
    co.set_argument('--disable-plugins')
    co.set_argument('--disable-images')  # 加快加载速度
    co.set_argument('--disable-javascript-harmony-shipping')
    co.set_argument('--disable-background-timer-throttling')
    co.set_argument('--disable-backgrounding-occluded-windows')
    co.set_argument('--disable-renderer-backgrounding')
    co.set_argument('--disable-features=TranslateUI')
    co.set_argument('--disable-ipc-flooding-protection')
    
    # 设置用户代理
    co.set_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    # 禁用开发者工具检测
    co.set_argument('--disable-dev-shm-usage')
    co.set_argument('--disable-web-security')
    co.set_argument('--disable-features=VizDisplayCompositor')
    
    # 设置窗口大小
    co.set_argument('--window-size=1920,1080')
    
    # 设置其他选项
    co.set_argument('--no-first-run')
    co.set_argument('--no-default-browser-check')
    co.set_argument('--disable-default-apps')
    
    return ChromiumPage(addr_or_opts=co)

# 初始化隐蔽模式浏览器
page = create_stealth_browser()

@mcp.tool()
def open_webpage_stealth(url: str) -> dict:
    """使用隐蔽模式打开网页"""
    try:
        # 注入反检测脚本
        page.run_js("""
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en'],
        });
        
        window.chrome = {
            runtime: {},
        };
        
        Object.defineProperty(navigator, 'permissions', {
            get: () => ({
                query: () => Promise.resolve({ state: 'granted' }),
            }),
        });
        """)
        
        page.get(url)
        return {"status": "success", "message": f"成功打开网页: {url}"}
    except Exception as e:
        return {"status": "error", "message": f"打开网页失败: {str(e)}"}

@mcp.tool()
def capture_dynamic_content(url: str, output_dir: str = "webpage_content", wait_time: int = 3) -> dict:
    """捕获动态内容，包括热力图数据"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    try:
        # 注入反检测脚本
        page.run_js("""
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // 禁用调试器检测
        var _constructor = constructor;
        Function.prototype.constructor = function(s) {
            if (s == "debugger") {
                return function() {};
            }
            return _constructor(s);
        };
        
        // 覆盖console.clear
        console.clear = function() {};
        
        // 禁用右键菜单检测
        document.addEventListener('contextmenu', function(e) {
            e.stopPropagation();
        }, true);
        
        // 禁用按键检测
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                e.stopPropagation();
                e.preventDefault();
            }
        }, true);
        """)
        
        # 打开网页
        page.get(url)
        
        # 等待页面加载完成
        time.sleep(3)
        
        # 等待Canvas元素加载
        try:
            page.wait.ele_displayed('canvas#treemap', timeout=10)
            print("Canvas元素已加载")
        except:
            print("Canvas元素未找到，继续等待...")
        
        # 等待数据加载
        time.sleep(wait_time)
        
        # 尝试获取页面中的JSON数据
        json_data = {}
        try:
            # 查找页面中的JSON数据
            json_scripts = page.eles('tag:script')
            for script in json_scripts:
                script_content = script.text
                if 'nuxt-data' in script_content or 'NUXT_DATA' in script_content:
                    # 尝试解析JSON数据
                    json_match = re.search(r'(\[.*\])', script_content)
                    if json_match:
                        try:
                            json_data = json.loads(json_match.group(1))
                            print("成功提取JSON数据")
                            break
                        except json.JSONDecodeError:
                            continue
        except Exception as e:
            print(f"提取JSON数据时出错: {e}")
        
        # 保存JSON数据
        if json_data:
            json_path = os.path.join(output_dir, "page_data.json")
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        # 尝试获取网络请求中的API数据
        api_data = {}
        try:
            # 监听网络请求（如果DrissionPage支持）
            # 这里需要根据具体的API调用来获取数据
            pass
        except Exception as e:
            print(f"监听网络请求时出错: {e}")
        
        # 获取完整的HTML内容
        html_content = page.html
        
        # 处理图片
        img_elements = page.eles('tag:img')
        img_urls = [img.attr('src') for img in img_elements if img.attr('src')]
        img_dir = os.path.join(output_dir, "images")
        if not os.path.exists(img_dir):
            os.makedirs(img_dir)
        
        img_count = 0
        img_map = {}
        for img_url in img_urls:
            if img_url:
                full_url = urljoin(url, img_url)
                try:
                    img_response = requests.get(full_url, timeout=5)
                    if img_response.status_code == 200:
                        img_name = os.path.basename(urlparse(full_url).path) or f"image_{img_count}.jpg"
                        img_path = os.path.join(img_dir, img_name)
                        with open(img_path, "wb") as f:
                            f.write(img_response.content)
                        img_map[img_url] = f"images/{img_name}"
                        img_count += 1
                except Exception as e:
                    print(f"下载图片失败 {full_url}: {str(e)}")
        
        # 替换HTML中的图片链接
        html_content = replace_image_links(html_content, img_map, url)
        
        # 保存HTML文件
        html_path = os.path.join(output_dir, "index.html")
        with open(html_path, "w", encoding="utf-8") as f:
            f.write(html_content)
        
        # 尝试截图
        screenshot_path = os.path.join(output_dir, "screenshot.png")
        try:
            page.get_screenshot(screenshot_path)
            print(f"截图已保存: {screenshot_path}")
        except Exception as e:
            print(f"截图失败: {e}")
        
        return {
            "status": "success",
            "message": f"成功保存网页内容到 {output_dir}",
            "details": {
                "html_saved": html_path,
                "images_downloaded": img_count,
                "json_data_found": len(json_data) > 0,
                "screenshot_saved": os.path.exists(screenshot_path)
            }
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"捕获动态内容失败: {str(e)}"
        }

@mcp.tool()
def extract_api_data(url: str, wait_time: int = 10) -> dict:
    """尝试直接提取API数据"""
    try:
        # 注入更强的反检测脚本
        page.run_js("""
        // 完全禁用调试器
        (function() {
            var _constructor = constructor;
            Function.prototype.constructor = function(s) {
                if (s == "debugger") {
                    return function() {};
                }
                return _constructor(s);
            };
            
            // 覆盖所有可能的调试检测
            var noop = function() {};
            window.console = window.console || {};
            var methods = ['assert', 'clear', 'count', 'debug', 'dir', 'dirxml', 'error', 'exception', 'group', 'groupCollapsed', 'groupEnd', 'info', 'log', 'markTimeline', 'profile', 'profileEnd', 'table', 'time', 'timeEnd', 'timeline', 'timelineEnd', 'timeStamp', 'trace', 'warn'];
            var length = methods.length;
            var console = window.console;
            while (length--) {
                console[methods[length]] = noop;
            }
            
            // 禁用页面离开检测
            window.addEventListener('beforeunload', function(e) {
                e.preventDefault();
                e.returnValue = '';
            });
            
            // 监听XHR请求
            window.apiData = [];
            var originalXHR = window.XMLHttpRequest;
            function newXHR() {
                var xhr = new originalXHR();
                xhr.addEventListener('load', function() {
                    if (xhr.responseText) {
                        try {
                            var data = JSON.parse(xhr.responseText);
                            window.apiData.push({
                                url: xhr.responseURL,
                                data: data,
                                status: xhr.status
                            });
                        } catch (e) {
                            // 不是JSON数据
                        }
                    }
                });
                return xhr;
            }
            window.XMLHttpRequest = newXHR;
            
            // 监听fetch请求
            var originalFetch = window.fetch;
            window.fetch = function() {
                return originalFetch.apply(this, arguments)
                    .then(function(response) {
                        if (response.ok) {
                            var clonedResponse = response.clone();
                            clonedResponse.json().then(function(data) {
                                window.apiData.push({
                                    url: response.url,
                                    data: data,
                                    status: response.status
                                });
                            }).catch(function() {
                                // 不是JSON数据
                            });
                        }
                        return response;
                    });
            };
        })();
        """)
        
        # 打开网页
        page.get(url)
        
        # 等待数据加载
        time.sleep(wait_time)
        
        # 获取捕获的API数据
        api_data = page.run_js("return window.apiData || [];")
        
        # 获取页面中的所有数据
        page_data = page.run_js("""
        return {
            title: document.title,
            url: window.location.href,
            nuxtData: window.__NUXT_DATA__ || null,
            nuxtApp: window.__NUXT__ || null,
            apiData: window.apiData || [],
            scripts: Array.from(document.scripts).map(s => s.textContent).filter(s => s.includes('data') || s.includes('json'))
        };
        """)
        
        return {
            "status": "success",
            "message": "成功提取API数据",
            "data": page_data
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"提取API数据失败: {str(e)}"
        }

def replace_image_links(html_content, img_map, base_url):
    """替换HTML中的图片链接"""
    for original_src, local_src in img_map.items():
        html_content = html_content.replace(original_src, local_src)
        if original_src.startswith('//'):
            html_content = html_content.replace(f"https:{original_src}", local_src)
            html_content = html_content.replace(f"http:{original_src}", local_src)
        elif original_src.startswith('/'):
            full_url = urljoin(base_url, original_src)
            html_content = html_content.replace(full_url, local_src)
    return html_content

def close():
    """关闭浏览器"""
    try:
        page.quit()
    except:
        pass

if __name__ == "__main__":
    mcp.run()
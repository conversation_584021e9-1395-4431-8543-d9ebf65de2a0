from playwright.sync_api import sync_playwright
import json
import time

def fetch_page_content(url):
    """
    使用 Playwright 获取网页内容，包括动态加载的 AJAX 数据。
    
    参数:
        url (str): 要获取内容的网页 URL
        
    返回:
        dict: 包含网页标题和内容的字典
    """
    with sync_playwright() as p:
        # 启动浏览器
        browser = p.chromium.launch(headless=False)  # headless=False 以便观察
        context = browser.new_context()
        page = context.new_page()
        
        # 导航到目标网址
        page.goto(url)
        
        # 等待页面加载完成，包括可能的 AJAX 请求
        page.wait_for_load_state('networkidle')  # 等待网络空闲状态
        time.sleep(2)  # 额外等待以确保动态内容加载
        
        # 获取页面标题
        title = page.title()
        
        # 获取页面内容（可以根据需要调整选择器）
        content = page.content()
        
        # 关闭浏览器
        browser.close()
        
        return {
            'title': title,
            'content': content
        }

def save_content(data, filename='webpage_content.json'):
    """
    将获取的内容保存到 JSON 文件。
    
    参数:
        data (dict): 要保存的数据
        filename (str): 保存文件的名称
    """
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print(f"内容已保存到 {filename}")

if __name__ == "__main__":
    # 示例 URL，可以根据需要修改
    target_url = "https://52etf.site/"
    print(f"正在获取网页内容: {target_url}")
    result = fetch_page_content(target_url)
    print(f"网页标题: {result['title']}")
    save_content(result, 'webpage_content.json')

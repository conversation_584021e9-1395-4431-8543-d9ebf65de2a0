import requests
import json
import time
from DrissionPage import ChromiumPage, ChromiumOptions
import os
import re
from urllib.parse import urljoin, urlparse

class ETFSiteScraper:
    def __init__(self):
        self.base_url = "https://52etf.site"
        self.session = requests.Session()
        self.setup_session()
        self.page = None
        
    def setup_session(self):
        """设置请求会话"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
    
    def create_stealth_browser(self):
        """创建隐蔽模式浏览器"""
        if self.page:
            return self.page
            
        co = ChromiumOptions()
        
        # 隐蔽模式配置
        co.add_argument('--no-sandbox')
        co.add_argument('--disable-blink-features=AutomationControlled')
        co.add_argument('--disable-extensions')
        co.add_argument('--disable-plugins')
        co.add_argument('--disable-dev-shm-usage')
        co.add_argument('--disable-web-security')
        co.add_argument('--disable-features=VizDisplayCompositor')
        co.add_argument('--window-size=1920,1080')
        co.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # 设置无头模式（可选）
        # co.add_argument('--headless')
        
        self.page = ChromiumPage(addr_or_opts=co)
        return self.page
    
    def inject_anti_detection_script(self):
        """注入反检测脚本"""
        script = """
        (function() {
            // 隐藏webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 模拟正常的插件
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // 设置语言
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            // 添加chrome对象
            window.chrome = {
                runtime: {},
                app: {},
                csi: {},
                loadTimes: () => ({
                    connectionInfo: 'http/1.1',
                    finishDocumentLoadTime: 1234567890,
                    finishLoadTime: 1234567890,
                    firstPaintAfterLoadTime: 1234567890,
                    firstPaintTime: 1234567890,
                    navigationType: 'Other',
                    npnNegotiatedProtocol: 'unknown',
                    requestTime: 1234567890,
                    startLoadTime: 1234567890,
                    wasAlternateProtocolAvailable: false,
                    wasFetchedViaSpdy: false,
                    wasNpnNegotiated: false
                })
            };
            
            // 禁用调试器检测
            var _constructor = constructor;
            Function.prototype.constructor = function(s) {
                if (s == "debugger") {
                    return function() {};
                }
                return _constructor(s);
            };
            
            // 覆盖console方法
            console.clear = function() {};
            
            // 禁用F12和右键检测
            document.addEventListener('keydown', function(e) {
                if (e.key === 'F12' || 
                    (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                    (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                    (e.ctrlKey && e.shiftKey && e.key === 'J')) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }
            }, true);
            
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }, true);
            
            // 监听网络请求
            window.capturedData = {
                api: [],
                websocket: [],
                fetch: []
            };
            
            // 拦截XMLHttpRequest
            var originalXHR = window.XMLHttpRequest;
            function newXHR() {
                var xhr = new originalXHR();
                var originalOpen = xhr.open;
                var originalSend = xhr.send;
                
                xhr.open = function(method, url, async, user, password) {
                    xhr._url = url;
                    xhr._method = method;
                    return originalOpen.apply(this, arguments);
                };
                
                xhr.send = function(data) {
                    xhr.addEventListener('load', function() {
                        if (xhr.responseText) {
                            try {
                                var responseData = JSON.parse(xhr.responseText);
                                window.capturedData.api.push({
                                    url: xhr._url,
                                    method: xhr._method,
                                    response: responseData,
                                    status: xhr.status
                                });
                            } catch (e) {
                                // 不是JSON数据
                            }
                        }
                    });
                    return originalSend.apply(this, arguments);
                };
                
                return xhr;
            }
            window.XMLHttpRequest = newXHR;
            
            // 拦截fetch
            var originalFetch = window.fetch;
            window.fetch = function() {
                return originalFetch.apply(this, arguments)
                    .then(function(response) {
                        if (response.ok) {
                            var clonedResponse = response.clone();
                            clonedResponse.json().then(function(data) {
                                window.capturedData.fetch.push({
                                    url: response.url,
                                    data: data,
                                    status: response.status
                                });
                            }).catch(function() {
                                // 不是JSON数据
                            });
                        }
                        return response;
                    });
            };
            
            // 尝试找到WebSocket连接
            var originalWebSocket = window.WebSocket;
            window.WebSocket = function(url, protocols) {
                var ws = new originalWebSocket(url, protocols);
                
                ws.addEventListener('message', function(event) {
                    try {
                        var data = JSON.parse(event.data);
                        window.capturedData.websocket.push({
                            url: url,
                            data: data,
                            timestamp: new Date().toISOString()
                        });
                    } catch (e) {
                        // 不是JSON数据
                    }
                });
                
                return ws;
            };
            
            console.log("反检测脚本已注入");
        })();
        """
        
        try:
            self.page.run_js(script)
            print("反检测脚本注入成功")
        except Exception as e:
            print(f"注入反检测脚本失败: {e}")
    
    def scrape_with_browser(self, output_dir="etf_data"):
        """使用浏览器爬取数据"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        try:
            # 创建浏览器
            page = self.create_stealth_browser()
            
            # 注入反检测脚本
            self.inject_anti_detection_script()
            
            print("正在访问网站...")
            page.get(self.base_url)
            
            # 等待页面加载
            time.sleep(5)
            
            # 等待Canvas元素
            try:
                page.wait.ele_displayed('canvas#treemap', timeout=15)
                print("热力图Canvas已加载")
            except Exception as e:
                print(f"等待Canvas元素失败: {e}")
            
            # 等待数据加载完成
            print("等待数据加载...")
            time.sleep(10)
            
            # 获取页面数据
            page_data = page.run_js("""
            return {
                title: document.title,
                url: window.location.href,
                nuxtData: window.__NUXT_DATA__ || null,
                nuxtApp: window.__NUXT__ || null,
                capturedData: window.capturedData || null,
                canvasElements: Array.from(document.querySelectorAll('canvas')).map(canvas => ({
                    id: canvas.id,
                    width: canvas.width,
                    height: canvas.height,
                    style: canvas.style.cssText
                }))
            };
            """)
            
            # 保存页面数据
            with open(os.path.join(output_dir, "page_data.json"), "w", encoding="utf-8") as f:
                json.dump(page_data, f, ensure_ascii=False, indent=2)
            
            # 获取完整HTML
            html_content = page.html
            with open(os.path.join(output_dir, "page.html"), "w", encoding="utf-8") as f:
                f.write(html_content)
            
            # 截图
            screenshot_path = os.path.join(output_dir, "screenshot.png")
            try:
                page.get_screenshot(screenshot_path)
                print(f"截图已保存: {screenshot_path}")
            except Exception as e:
                print(f"截图失败: {e}")
            
            # 尝试获取API数据
            api_data = page.run_js("return window.capturedData || {};")
            if api_data:
                with open(os.path.join(output_dir, "api_data.json"), "w", encoding="utf-8") as f:
                    json.dump(api_data, f, ensure_ascii=False, indent=2)
                print("API数据已保存")
            
            return {
                "status": "success",
                "message": f"数据已保存到 {output_dir}",
                "data": page_data
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"爬取失败: {str(e)}"
            }
    
    def try_api_endpoints(self):
        """尝试直接访问API端点"""
        possible_endpoints = [
            "/api/treemap",
            "/api/data",
            "/api/stocks",
            "/api/market",
            "/_nuxt/api/data",
            "/data.json",
            "/api/etf",
            "/api/realtime"
        ]
        
        results = {}
        for endpoint in possible_endpoints:
            try:
                url = urljoin(self.base_url, endpoint)
                response = self.session.get(url, timeout=10)
                if response.status_code == 200:
                    try:
                        data = response.json()
                        results[endpoint] = data
                        print(f"成功获取API数据: {endpoint}")
                    except json.JSONDecodeError:
                        results[endpoint] = response.text
                        print(f"获取到非JSON数据: {endpoint}")
                else:
                    print(f"API端点 {endpoint} 返回状态码: {response.status_code}")
            except Exception as e:
                print(f"访问API端点 {endpoint} 失败: {e}")
        
        return results
    
    def close(self):
        """关闭浏览器"""
        if self.page:
            try:
                self.page.quit()
            except:
                pass

# 使用示例
def main():
    scraper = ETFSiteScraper()
    
    try:
        # 尝试直接访问API
        print("尝试直接访问API端点...")
        api_results = scraper.try_api_endpoints()
        if api_results:
            print("找到API数据:")
            for endpoint, data in api_results.items():
                print(f"  {endpoint}: {type(data)}")
        
        # 使用浏览器爬取
        print("\n使用浏览器爬取...")
        result = scraper.scrape_with_browser()
        print(f"爬取结果: {result['status']}")
        print(f"消息: {result['message']}")
        
    finally:
        scraper.close()

if __name__ == "__main__":
    main()
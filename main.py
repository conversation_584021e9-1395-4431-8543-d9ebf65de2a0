import os

from mcp.server.fastmcp import FastMCP
from DrissionPage import ChromiumPage
import requests
from urllib.parse import urljoin, urlparse

# 创建 MCP 服务器
mcp = FastMCP("DrissionPage Server")

# 全局浏览器实例变量
page = None

def get_page():
    """获取或创建浏览器实例"""
    global page
    if page is None:
        page = ChromiumPage()
    return page

@mcp.tool()
def open_webpage(url: str) -> dict:
    """Open a webpage in the browser."""
    browser_page = get_page()
    browser_page.get(url)
    return {"status": "success", "message": f"Opened webpage: {url}"}

@mcp.tool()
def save_webpage_content(url: str, output_dir: str = "webpage_content", wait_time: int = 10) -> dict:
    """Save the webpage source code, image resources, and dynamic data to the current directory."""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    browser_page = get_page()
    browser_page.get(url)
    # Wait for page to load completely, including dynamic content
    # page.wait.load_complete(timeout=wait_time)
    
    # Listen for network requests to capture API calls (if supported by DrissionPage)
    # Note: DrissionPage might not directly support network request interception like Selenium.
    # If direct interception is not supported, consider alternative approaches below.
    # network_requests = page.listen.start(['request', 'response'])
    # page.wait(wait_time)  # Wait for network activity
    
    # Alternatively, wait for specific elements that indicate data has loaded
    # page.wait.ele_displayed('selector-for-data-element', timeout=wait_time)
    
    # Optionally, simulate scrolling to trigger lazy-loaded content
    # page.actions.scroll_to_bottom()
    
    # Save images
    img_elements = browser_page.eles('tag:img')
    img_urls = [img.attr('src') for img in img_elements if img.attr('src')]
    img_dir = os.path.join(output_dir, "images")
    if not os.path.exists(img_dir):
        os.makedirs(img_dir)
    
    img_count = 0
    img_map = {}
    for img_url in img_urls:
        if img_url:
            full_url = urljoin(url, img_url)
            try:
                img_response = requests.get(full_url, timeout=5)
                if img_response.status_code == 200:
                    img_name = os.path.basename(urlparse(full_url).path) or f"image_{img_count}.jpg"
                    img_path = os.path.join(img_dir, img_name)
                    with open(img_path, "wb") as f:
                        f.write(img_response.content)
                    # 处理相对路径和以 // 开头的路径
                    if img_url.startswith('//'):
                        img_map[img_url] = f"images/{img_name}"
                        img_map[f"https:{img_url}"] = f"images/{img_name}"
                        img_map[f"http:{img_url}"] = f"images/{img_name}"
                    elif img_url.startswith('/'):
                        img_map[img_url] = f"images/{img_name}"
                        img_map[urljoin(url, img_url)] = f"images/{img_name}"
                    else:
                        img_map[img_url] = f"images/{img_name}"
                    img_count += 1
            except Exception as e:
                print(f"Failed to download image {full_url}: {str(e)}")
    
    # Attempt to extract dynamic data from rendered content
    html_content = browser_page.html

    # Try to get dynamic content if it exists
    try:
        dynamic_element = browser_page.ele("css:#dynamic-content", timeout=1)
        dynamic_content = dynamic_element.text if dynamic_element else ""
    except Exception:
        dynamic_content = ""
    
    # If direct data extraction from HTML is not sufficient, consider extracting API URLs
    # from JavaScript or network requests (if possible) and fetching data directly.
    # For now, save the rendered HTML content.
    html_content = replace_image_links(html_content, img_map, url)
    html_path = os.path.join(output_dir, "index.html")
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_content)
    
    return {
        "status": "success",
        "message": f"Saved webpage content to {html_path} and downloaded {img_count} images to {img_dir}. Note: Dynamic data might still be missing if loaded via asynchronous API calls not captured by page rendering."
    }

def replace_image_links(html_content, img_map, base_url):
    """Replace image links in HTML content with local paths."""
    for original_src, local_src in img_map.items():
        # Replace original source
        html_content = html_content.replace(original_src, local_src)
        # Handle protocol-relative URLs
        if original_src.startswith('//'):
            html_content = html_content.replace(f"https:{original_src}", local_src)
            html_content = html_content.replace(f"http:{original_src}", local_src)
        # Handle absolute URLs derived from relative paths
        elif original_src.startswith('/'):
            full_url = urljoin(base_url, original_src)
            html_content = html_content.replace(full_url, local_src)
            # Also replace with protocol-relative form
            protocol_relative = f"//{urlparse(full_url).netloc}{urlparse(full_url).path}"
            if urlparse(full_url).query:
                protocol_relative += f"?{urlparse(full_url).query}"
            if urlparse(full_url).fragment:
                protocol_relative += f"#{urlparse(full_url).fragment}"
            html_content = html_content.replace(protocol_relative, local_src)
        # Also check if the original_src is already a full URL in img_map
        elif original_src.startswith('http://') or original_src.startswith('https://'):
            # If it's a full URL, replace it directly
            html_content = html_content.replace(original_src, local_src)
            # Also try replacing without the protocol for protocol-relative matching
            protocol_relative_src = original_src.replace('https:', '').replace('http:', '')
            html_content = html_content.replace(protocol_relative_src, local_src)
    return html_content

def close():
    """Close the browser when the server shuts down."""
    global page
    if page is not None:
        page.quit()
        page = None

if __name__ == "__main__":
    mcp.run()
